import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

export const CustomToast = ({ text1, text2, props }: any) => {
    return (
        <View style={styles.toastContainer}>
            <Text style={styles.title}>{text1}</Text>
            {text2 ? <Text style={styles.subtitle}>{text2}</Text> : null}
        </View>
    );
};

const styles = StyleSheet.create({
    toastContainer: {
        backgroundColor: '#ff4d4f',
        padding: 15,
        borderRadius: 10,
        marginHorizontal: 10,
    },
    title: {
        color: 'white',
        fontWeight: 'bold',
    },
    subtitle: {
        color: 'white',
        marginTop: 5,
    },
});
