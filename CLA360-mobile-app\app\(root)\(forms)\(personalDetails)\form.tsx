import React, { useState } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    ScrollView,
    Animated,
    Dimensions,
    Platform
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { router } from "expo-router";
import InputField from "@/components/InputField";
import { Countries } from "@/constants";
import SelectDropDown from "@/components/selectDropDown";
import { Picker } from "@react-native-picker/picker";
import DatePicker from "@/components/datePicker";

const { width: screenWidth } = Dimensions.get('window');



const ProgressBar = ({ currentStep, totalSteps }: any) => (
    <View className="mb-8">
        <View className="flex-row justify-between items-center mb-2">
            <Text className="text-sm font-medium text-gray-600">
                Step {currentStep} of {totalSteps}
            </Text>
            <Text className="text-sm font-medium text-blue-600">
                {Math.round((currentStep / totalSteps) * 100)}% Complete
            </Text>
        </View>
        <View className="h-2 bg-gray-100 rounded-full overflow-hidden">
            <View
                className="h-full bg-gradient-to-r from-blue-500 to-purple-600 rounded-full transition-all duration-300"
                style={{ width: `${(currentStep / totalSteps) * 100}%` }}
            />
        </View>
    </View>
);

const StepIndicator = ({ steps, currentStep }: any) => (
    <View className="flex-row justify-center items-center mb-8 px-4">
        {steps.map((step: any, index: any) => (
            <React.Fragment key={index}>
                <View className="items-center">
                    <View
                        className={`w-10 h-10 rounded-full items-center justify-center mb-2 ${index < currentStep
                            ? 'bg-green-500'
                            : index === currentStep
                                ? 'bg-blue-500'
                                : 'bg-gray-200'
                            }`}
                    >
                        {index < currentStep ? (
                            <Ionicons name="checkmark" size={20} color="white" />
                        ) : (
                            <Text className={`font-semibold ${index === currentStep ? 'text-white' : 'text-gray-500'
                                }`}>
                                {index + 1}
                            </Text>
                        )}
                    </View>
                    <Text className={`text-xs font-medium text-center max-w-16 ${index <= currentStep ? 'text-gray-900' : 'text-gray-500'
                        }`}>
                        {step}
                    </Text>
                </View>
                {index < steps.length - 1 && (
                    <View className={`flex-1 h-0.5 mx-2 ${index < currentStep ? 'bg-green-500' : 'bg-gray-200'
                        }`} />
                )}
            </React.Fragment>
        ))}
    </View>
);

const FormSection = ({ title, icon, children }: any) => (
    <View className="mb-8">
        <View className="flex-row items-center mb-6">
            <View className="w-8 h-8 bg-blue-100 rounded-full items-center justify-center mr-3">
                <Ionicons name={icon} size={18} color="#3B82F6" />
            </View>
            <Text className="text-lg font-semibold text-gray-900">{title}</Text>
        </View>
        <View className="space-y-4">
            {children}
        </View>
    </View>
);

const PremiumPersonalDetailsForm = () => {
    const [currentStep, setCurrentStep] = useState(0);
    const [personalData, setPersonalData] = useState({
        // Basic Info
        dateOfBirth: null,
        gender: '',
        nationality: '',

        // Birth Details
        countryOfBirth: '',
        stateOfBirth: '',
        cityOfBirth: '',

        // Personal Status
        maritalStatus: '',

        // Address
        primaryAddress: {
            line_1: '',
            line_2: '',
            line_3: '',
            country_id: '',
            state: '',
            city: '',
            zipCode: '',
        },
    });

    const steps = ['Basic Info', 'Birth Details', 'Address', 'Review'];
    const totalSteps = steps.length;

    const genderOptions = ['Male', 'Female', 'Other', 'Prefer not to say'];
    const maritalStatusOptions = ['Single', 'Married', 'Divorced', 'Widowed'];


    const handleNext = () => {
        if (currentStep < totalSteps - 1) {
            setCurrentStep(currentStep + 1);
        } else {
            router.push("/(root)/(forms)/(gaurdianDetails)/form");

        }
    };

    const handlePrevious = () => {
        if (currentStep > 0) {
            setCurrentStep(currentStep - 1);
        }
    };

    const renderStep = () => {
        switch (currentStep) {
            case 0:
                return (
                    <FormSection title="Basic Information" icon="person-outline">
                        <DatePicker
                            value={personalData.dateOfBirth}
                            required
                            label="Date of Birth"
                            onChange={(value: any) =>
                                setPersonalData({ ...personalData, dateOfBirth: value })
                            }
                        />
                        <SelectDropDown label="Gender">
                            <Picker
                                style={{ flex: 1 }}
                                selectedValue={personalData.gender}
                                onValueChange={(itemValue, itemIndex) =>
                                    setPersonalData({ ...personalData, gender: itemValue })
                                }
                            >
                                {genderOptions?.map((status, index) => (
                                    <Picker.Item label={status} value={status} key={index} />
                                ))}
                            </Picker>
                        </SelectDropDown>
                        <SelectDropDown label="Nationality">
                            <Picker style={{ flex: 1 }}
                                onValueChange={(itemValue, itemIndex) =>
                                    setPersonalData({ ...personalData, nationality: itemValue })
                                }
                                selectedValue={personalData.nationality}
                            >
                                {Countries?.map((country) => (
                                    <Picker.Item
                                        label={country.nationality}
                                        value={country.id}
                                        key={country.id}
                                    />
                                ))}
                            </Picker>
                        </SelectDropDown>
                    </FormSection>
                );

            case 1:
                return (
                    <FormSection title="Birth Details" icon="location-outline">
                        <SelectDropDown
                            label="Country of Birth"
                        >
                            <Picker style={{ flex: 1 }}
                                selectedValue={personalData.countryOfBirth}
                                onValueChange={(itemValue, itemIndex) =>
                                    setPersonalData({ ...personalData, countryOfBirth: itemValue })
                                }
                            >
                                {Countries?.map((country) => (
                                    <Picker.Item
                                        label={country.name}
                                        value={country.id}
                                        key={country.id}
                                    />
                                ))}
                            </Picker>
                        </SelectDropDown>
                        <InputField
                            label="State/Province of Birth"
                            value={personalData.stateOfBirth}
                            onChangeText={(value) => setPersonalData({ ...personalData, stateOfBirth: value })}
                            placeholder="Enter state or province"
                            icon="map-outline"
                        />
                        <InputField
                            label="City of Birth"
                            value={personalData.cityOfBirth}
                            onChangeText={(value) => setPersonalData({ ...personalData, cityOfBirth: value })}
                            placeholder="Enter city"
                            icon="business-outline"
                        />
                        <SelectDropDown label="Marital Status">
                            <Picker
                                style={{ flex: 1 }}
                                selectedValue={personalData.maritalStatus}
                                onValueChange={(itemValue, itemIndex) =>
                                    setPersonalData({ ...personalData, maritalStatus: itemValue })
                                }
                            >
                                {maritalStatusOptions?.map((status, index) => (
                                    <Picker.Item label={status} value={status} key={index} />
                                ))}
                            </Picker>
                        </SelectDropDown>
                    </FormSection>
                );

            case 2:
                return (
                    <FormSection title="Current Address" icon="home-outline">
                        <InputField
                            label="Address Line 1"
                            value={personalData.primaryAddress.line_1}
                            onChangeText={(value) => setPersonalData({
                                ...personalData,
                                primaryAddress: { ...personalData.primaryAddress, line_1: value }
                            })}
                            placeholder="Street address"
                            icon="location-outline"
                            required
                        />
                        <InputField
                            label="Address Line 2"
                            value={personalData.primaryAddress.line_2}
                            onChangeText={(value) => setPersonalData({
                                ...personalData,
                                primaryAddress: { ...personalData.primaryAddress, line_2: value }
                            })}
                            placeholder="Apartment, suite, etc. (optional)"
                            icon="business-outline"
                        />
                        <SelectDropDown label="Country"
                        >
                            <Picker style={{ flex: 1 }}
                                selectedValue={personalData.primaryAddress.country_id}
                                onValueChange={(itemValue, itemIndex) =>
                                    setPersonalData({
                                        ...personalData,
                                        primaryAddress: {
                                            ...personalData.primaryAddress,
                                            country_id: itemValue,
                                        },
                                    })
                                }
                            >
                                {Countries?.map((country) => (
                                    <Picker.Item
                                        label={country.name}
                                        value={country.id}
                                        key={country.id}
                                    />
                                ))}
                            </Picker>
                        </SelectDropDown>
                        <View className="flex-row space-x-3">
                            <View className="flex-1">
                                <InputField
                                    label="State/Province"
                                    value={personalData.primaryAddress.state}
                                    onChangeText={(value) => setPersonalData({
                                        ...personalData,
                                        primaryAddress: { ...personalData.primaryAddress, state: value }
                                    })}
                                    placeholder="State"
                                    icon="map-outline"
                                />
                            </View>
                            <View className="flex-1">
                                <InputField
                                    label="City"
                                    value={personalData.primaryAddress.city}
                                    onChangeText={(value) => setPersonalData({
                                        ...personalData,
                                        primaryAddress: { ...personalData.primaryAddress, city: value }
                                    })}
                                    placeholder="City"
                                    icon="business-outline"
                                />
                            </View>
                        </View>
                        <InputField
                            label="Postal/Zip Code"
                            value={personalData.primaryAddress.zipCode}
                            onChangeText={(value) => setPersonalData({
                                ...personalData,
                                primaryAddress: { ...personalData.primaryAddress, zipCode: value }
                            })}
                            placeholder="12345"
                            icon="mail-outline"
                        />
                    </FormSection>
                );

            case 3:
                return (
                    <FormSection title="Review Your Information" icon="checkmark-circle-outline">
                        <View className="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm">
                            <Text className="text-lg font-semibold text-gray-900 mb-4">Personal Details Summary</Text>

                            <View className="space-y-3">
                                <View className="flex-row justify-between">
                                    <Text className="text-gray-600">Date of Birth:</Text>
                                    <Text className="font-medium text-gray-900">
                                        {personalData.dateOfBirth?.toLocaleDateString() || 'Not provided'}
                                    </Text>
                                </View>
                                <View className="flex-row justify-between">
                                    <Text className="text-gray-600">Gender:</Text>
                                    <Text className="font-medium text-gray-900">{personalData.gender || 'Not provided'}</Text>
                                </View>
                                <View className="flex-row justify-between">
                                    <Text className="text-gray-600">Nationality:</Text>
                                    <Text className="font-medium text-gray-900">{personalData.nationality || 'Not provided'}</Text>
                                </View>
                                <View className="flex-row justify-between">
                                    <Text className="text-gray-600">Country of Birth:</Text>
                                    <Text className="font-medium text-gray-900">{personalData.countryOfBirth || 'Not provided'}</Text>
                                </View>
                                <View className="flex-row justify-between">
                                    <Text className="text-gray-600">Address:</Text>
                                    <Text className="font-medium text-gray-900 text-right flex-1 ml-4">
                                        {personalData.primaryAddress.line_1 || 'Not provided'}
                                    </Text>
                                </View>
                            </View>

                            <TouchableOpacity className="mt-6 p-3 bg-blue-50 rounded-xl">
                                <Text className="text-blue-600 text-center font-medium">Edit Information</Text>
                            </TouchableOpacity>
                        </View>
                    </FormSection>
                );

            default:
                return null;
        }
    };

    return (
        <View className="flex-1 bg-gray-50">
            {/* Header */}
            <View className="bg-white px-6 pt-12 pb-6 shadow-sm">
                <View className="flex-row items-center justify-between mb-4">
                    <TouchableOpacity
                        onPress={handlePrevious}
                        className={`w-10 h-10 rounded-full items-center justify-center ${currentStep === 0 ? 'bg-gray-100' : 'bg-gray-200'
                            }`}
                        disabled={currentStep === 0}
                    >
                        <Ionicons
                            name="chevron-back"
                            size={20}
                            color={currentStep === 0 ? "#9CA3AF" : "#374151"}
                        />
                    </TouchableOpacity>
                    <Text className="text-xl font-bold text-gray-900">Personal Details</Text>
                    <View className="w-10 h-10" />
                </View>

                <ProgressBar currentStep={currentStep + 1} totalSteps={totalSteps} />
            </View>

            {/* Step Indicator */}
            <StepIndicator steps={steps} currentStep={currentStep} />

            {/* Form Content */}
            <ScrollView
                className="flex-1 px-6"
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{ paddingBottom: 120 }}
            >
                {renderStep()}
            </ScrollView>

            {/* Bottom Actions */}
            <View className="absolute bottom-0 left-0 right-0 bg-white p-6 shadow-lg">
                <LinearGradient
                    colors={['rgba(255,255,255,0)', 'rgba(255,255,255,1)']}
                    className="absolute top-0 left-0 right-0 h-4"
                />

                <View className="flex-row space-x-3">
                    {currentStep > 0 && (
                        <TouchableOpacity
                            onPress={handlePrevious}
                            className="flex-1 p-4 rounded-2xl border border-gray-300 bg-white"
                        >
                            <Text className="text-center font-semibold text-gray-700">Previous</Text>
                        </TouchableOpacity>
                    )}

                    <TouchableOpacity
                        onPress={handleNext}
                        className="flex-1 p-4 rounded-2xl bg-gradient-to-r from-blue-600 to-purple-600"
                    >
                        <Text className="text-center font-semibold text-white">
                            {currentStep === totalSteps - 1 ? 'Complete' : 'Continue'}
                        </Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
};

export default PremiumPersonalDetailsForm;
