import { Image, ImageSourcePropType, Platform, StyleSheet, Text, View } from 'react-native'
import React from 'react'
import { Tabs } from 'expo-router'
import { icons } from '@/constants'
import Ionicons from '@expo/vector-icons/Ionicons';
import CustomTabBar from "@/components/customTabBar/CustomBar";

const TabIcon = ({ source, focused, label }: { source: ImageSourcePropType; focused: boolean; label: string }) => (
    <View className={`flex flex-col w-16 justify-center items-center`}>
        <View className={`rounded-md w-12 h-12 justify-center items-center ${focused ? "bg-blue-500" : ""}`}>
            <Image source={source} tintColor={`${focused ? "#fff" : "#333333"}`} resizeMode='contain' className='w-7 h-7' />
        </View>
        <Text className="font-JakartaSemiBold text-xs">{label}</Text>
    </View>
)

const Layout = () => {
    return (
        <Tabs initialRouteName='home' screenOptions={{
            tabBarActiveTintColor: "white",
            tabBarInactiveTintColor: "white",
            tabBarShowLabel: true,
            // tabBarStyle: {
            //     backgroundColor: "#fff",
            //     // borderRadius: 50,
            //     // paddingBottom: 0,
            //     paddingHorizontal: 0,
            //     paddingBottom: 50,
            //     height: 85,
            //     display: "flex",
            //     flexDirection: "row",
            //     justifyContent: "center",
            //     alignItems: "center",
            //     position: "absolute",
            //     overflow: "hidden",
            //     borderTopColor: "#333333"
            // }
            tabBarStyle: Platform.select({
                ios: {
                    // Use a transparent background on iOS to show the blur effect
                    position: 'absolute',
                },
                default: {},
            }),
        }} tabBar={(props: any) => <CustomTabBar {...props} />}
        >
            <Tabs.Screen
                name='home'
                options={{
                    title: "Home",
                    headerShown: false,
                    // tabBarIcon: ({ focused }) => <TabIcon focused={focused} source={icons.home} label='Home' />
                }}
            />
            <Tabs.Screen
                name='schools'
                options={{
                    title: "Applications",
                    // tabBarIcon: ({ focused }) => <TabIcon focused={focused} source={icons.track} label='Track' />
                }}
            />
            <Tabs.Screen
                name='search'
                options={{
                    title: "Search",
                    // tabBarIcon: ({ focused }) => <TabIcon focused={focused} source={icons.search} label='Search' />
                }}
            />
            {/* <Tabs.Screen
                name='apply'
                options={{
                    title: "Apply",
                    tabBarIcon: ({ focused }) => <TabIcon focused={focused} source={icons.list} label='Apply' />
                }}
            /> */}
        </Tabs>
    )
}

export default Layout
