import { useMutation, useQuery } from "@tanstack/react-query";

import { userService, studentService } from "@/api/index";
import { type SignInReq, type SignInRes } from "@/api/index";
import { type UserInfo } from "@/types/entity";
import { APIError } from "@/api/apiClient";
import Toast from "react-native-toast-message";

export const useEmailVerification = () => {
    return useMutation({
        mutationFn: (data: any) => userService.emailVerification(data),
        onError: (err: any) => {
            // Use user-friendly message from APIError if available
            const errorMessage =
                err instanceof APIError
                    ? err.userMessage
                    : err.message || "Email verification failed";

            Toast.show({
                type: "error",
                text1: errorMessage,
            });
        },
    });
};

export const useChangePassword = () => {
    return useMutation({
        mutationFn: (data) => userService.ChangePassword(data),
    });
};

export const useAddProfile = () => {
    return useMutation({
        mutationFn: (data) => studentService.createProfile(data),
    });
};

export const useAddPersonalInfo = () => {
    return useMutation({
        mutationFn: (data) => studentService.createPersonalInfo(data),
    });
};

export const useAddGuardianInfo = () => {
    return useMutation({
        mutationFn: (data) => studentService.createGuardianInfo(data),
    });
};

export const useAddAcademicInfo = () => {
    return useMutation({
        mutationFn: (data) => studentService.createAcademicInfo(data),
    });
};

export const useAddProgramPreference = () => {
    return useMutation({
        mutationFn: (data) => studentService.createProgramPreference(data),
    });
};

export const useAddFinancialInformation = () => {
    return useMutation({
        mutationFn: (data) => studentService.createFinancialInformation(data),
    });
};

export const useAddExtracurricularActivities = () => {
    return useMutation({
        mutationFn: (data) =>
            studentService.createExtracurricularActivities(data),
    });
};

export const useAddWorkExperience = () => {
    return useMutation({
        mutationFn: (data) => studentService.createWorkExperience(data),
    });
};

export const useAddHealthInformation = () => {
    return useMutation({
        mutationFn: (data) => studentService.createHealthInformation(data),
    });
};

export const useAddAdditionalInformation = () => {
    return useMutation({
        mutationFn: (data) => studentService.createAdditionalInformation(data),
    });
};

export const useAddFundingHub = () => {
    return useMutation({
        mutationFn: (data) => studentService.createFundingHub(data),
    });
};

export const useAddAiReWrite = () => {
    return useMutation({
        mutationFn: (data) => studentService.createAiReWrite(data),
    });
};

export const useAddWaecUpload = () => {
    return useMutation({
        mutationFn: (data) => studentService.createWaecUpload(data),
    });
};

export const useAddPaymentStripe = () => {
    return useMutation({
        mutationFn: (data) => studentService.createPaymentStripe(data),
    });
};

export const useAddPaymentPromo = () => {
    return useMutation({
        mutationFn: (data) => studentService.createPaymentPromo(data),
    });
};
