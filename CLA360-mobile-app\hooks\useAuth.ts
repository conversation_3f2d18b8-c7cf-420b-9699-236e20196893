import { useUserInfo, useUserToken, useUserActions } from '@/store/userStore';

export const useAuth = () => {
    const userInfo = useUserInfo();
    const userToken = useUserToken();
    const { clearUserInfoAndToken } = useUserActions();

    // Check if user is signed in based on the presence of access token and user info
    const isSignedIn = !!(userToken?.access_token && userInfo?.student?.id);

    // Check if user is loaded (has attempted to load from storage)
    const isLoaded = true; // Since we're using Zustand with persistence, this is always true

    const signOut = () => {
        clearUserInfoAndToken();
    };

    return {
        isSignedIn,
        isLoaded,
        user: userInfo?.student || null,
        userToken,
        signOut,
    };
};
