import { BlurView } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import { useState } from 'react';
import { View, Text, Pressable, Image, ScrollView, StyleSheet } from 'react-native';
import Animated, { FadeIn, FadeInUp, FadeOut, SlideInUp } from 'react-native-reanimated';
import { images } from '@/constants'
import Card from "@/components/Card";
import Marquee from "@/components/Marquee";

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

const events = [
    {
        id: 1,
        image: images.transcript,
        title: "Request Transcript",
        route: "/(root)/transcripts"
    },
    {
        id: 2,
        image: images.transcript1,
        title: "Request Evaluation",
        route: "/(root)/evaluations"
    },
    {
        id: 3,
        image: images.applications,
        title: "Start Applications",
        route: "/(root)/(tabs)/schools"
    },
    {
        id: 4,
        image: images.search1,
        title: "University Search",
        route: "/(root)/(tabs)/search"
    },
    {
        id: 6,
        image: images.pay,
        title: "Payment",
        route: "/(root)/payment"
    }
];

export default function Exhibition() {
    const [activeIndex, setActiveIndex] = useState(0);

    const handleItemPress = (item: any) => {
        if (item.route) {
            router.push(item.route);
        }
    };

    return (
        <View className="flex-1">
            <BlurView intensity={70} className="flex-1">
                <Animated.View
                    className="w-full flex-1"
                    entering={SlideInUp.springify().mass(1).damping(30)}>
                    <Marquee
                        items={events}
                        renderItem={({ item }) => (
                            <Card
                                event={item}
                                onPress={() => handleItemPress(item)}
                            />
                        )}
                        onIndexChange={setActiveIndex}
                        speed={5}
                    />
                </Animated.View>
            </BlurView>
        </View>
    );
}
