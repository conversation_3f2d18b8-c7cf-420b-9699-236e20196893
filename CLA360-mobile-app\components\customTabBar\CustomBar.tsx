/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { View, Text, TouchableOpacity, StyleSheet, Dimensions } from 'react-native'

import { useEffect, useState } from 'react'
import Animated, {
    useAnimatedStyle,
    useSharedValue,
    withSpring,
    withTiming,
} from 'react-native-reanimated'
import { BottomTabBarProps } from '@react-navigation/bottom-tabs'
const hp = (percentage: number) => {
    return (Dimensions.get('window').height * percentage) / 100
}
import TabBarIcon from './TabBarIcon'

const { width, height } = Dimensions.get('window')

export default function CustomTabBar({ state, descriptors, navigation }: BottomTabBarProps) {
    const translateX = useSharedValue(0)
    const [dimentions, setDimentions] = useState({ width: 200, height: 100 })

    const buttonWidth = dimentions.width / state.routes.length

    const onTabBarLayout = (e: any) => {
        // console.log(e.nativeEvent);
        setDimentions({
            width: e.nativeEvent.layout.width,
            height: e.nativeEvent.layout.height,
        })
    }

    useEffect(() => {
        translateX.value = withTiming(buttonWidth * state.index, {
            duration: 300,
        })
    }, [state.index])

    const rCircle = useAnimatedStyle(() => {
        return {
            transform: [{ translateX: translateX.value }],
        }
    })

    return (
        <View style={styles.tabBarContainer} onLayout={onTabBarLayout}>
            <Animated.View
                style={[
                    rCircle,
                    {
                        width: buttonWidth - 12,
                        height: dimentions.height - 15,
                        position: 'absolute',
                        zIndex: -1,
                        marginHorizontal: 6,
                        justifyContent: 'center',
                        alignItems: 'center',
                    },
                ]}
            >
                <View style={styles.cicrle}></View>
            </Animated.View>
            {state.routes.map((route: any, index: any) => {
                const descriptor = descriptors[route.key]
                if (!descriptor) {
                    return null
                }
                const { options } = descriptor
                const label =
                    options.tabBarLabel === 'string'
                        ? options.tabBarLabel
                        : options.title !== undefined
                            ? options.title
                            : route.name

                const isFocused = state.index === index

                const onPress = () => {
                    const event = navigation.emit({
                        type: 'tabPress',
                        target: route.key,
                        canPreventDefault: true,
                    })

                    if (!isFocused && !event.defaultPrevented) {
                        navigation.navigate(route.name, route.params)
                    }
                }

                const onLongPress = () => {
                    navigation.emit({
                        type: 'tabLongPress',
                        target: route.key,
                    })
                }

                return <TabBarIcon key={route.key} onPress={onPress} isFocused={isFocused} label={label} />
            })}
        </View>
    )
}

const styles = StyleSheet.create({
    tabBarContainer: {
        flexDirection: 'row',
        height: hp(8.5),
        position: 'absolute',
        alignSelf: 'center',
        backgroundColor: '#86D17D',
        borderRadius: 100,
        alignItems: 'center',
        justifyContent: 'space-evenly',
        width: width * 0.5,
        // width: width * 0.95,
        bottom: hp(2.6),
        // paddingVertical: 15,
        shadowColor: 'black',
        shadowOffset: { height: 10, width: 0 },
        shadowRadius: 15,
        shadowOpacity: 0.5,
        elevation: 15,
    },
    cicrle: {
        width: 15,
        height: 15,
        backgroundColor: '#151718',
        borderRadius: 10,
        marginBottom: 18,
    },
})
