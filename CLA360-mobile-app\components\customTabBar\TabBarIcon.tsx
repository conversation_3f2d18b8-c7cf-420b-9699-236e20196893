/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { StyleSheet, Text, View, TouchableOpacity, Dimensions } from 'react-native'
import React, { useEffect } from 'react'
import Feather from '@expo/vector-icons/Feather'
import MaterialIcons from '@expo/vector-icons/MaterialIcons'
import Animated, {
    interpolate,
    useAnimatedStyle,
    useSharedValue,
    withTiming,
} from 'react-native-reanimated'
import { GestureResponderEvent } from 'react-native'
const hp = (percentage: number) => {
    return (Dimensions.get('window').height * percentage) / 100
}

type Props = {
    onPress: (e: GestureResponderEvent) => void
    isFocused: boolean
    label?: string
}

const icons: { [key: string]: (props: any) => JSX.Element } = {
    Home: (props: any) => <Feather name="home" size={24} {...props} />,
    Applications: (props: any) => <MaterialIcons name="track-changes" size={24} {...props} />,
    Search: (props: any) => <Feather name="search" size={24} {...props} />,
}

const TabBarIcon = ({ onPress, isFocused, label }: Props) => {
    const translateY = useSharedValue(0)
    const scale = useSharedValue(0)
    const textOpacity = useSharedValue(0)
    const iconOpacity = useSharedValue(0)

    useEffect(() => {
        textOpacity.value = withTiming(isFocused ? 1 : 0, { duration: 400 })
        iconOpacity.value = withTiming(isFocused ? 0 : 1, { duration: 400 })
        translateY.value = withTiming(isFocused ? 12 : 0, { duration: 400 })
    }, [isFocused])

    const rText = useAnimatedStyle(() => {
        return {
            opacity: textOpacity.value,
            transform: [{ translateY: translateY.value }],
        }
    })

    const rImage = useAnimatedStyle(() => {
        return {
            opacity: iconOpacity.value,
        }
    })

    return (
        <TouchableOpacity
            onPress={onPress}
            style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
                gap: 5,
            }}
        >
            <Animated.View style={[rImage, { position: 'absolute' }]}>
                {label && icons[label] ? icons[label]({ color: '#fff' }) : null}
            </Animated.View>

            <Animated.Text
                style={[
                    {
                        color: 'white',
                        fontWeight: 600,
                        fontFamily: 'mon-bold',
                        fontSize: hp(1.4),
                    },
                    rText,
                ]}
            >
                {label}
            </Animated.Text>
        </TouchableOpacity>
    )
}

export default TabBarIcon

const styles = StyleSheet.create({})
