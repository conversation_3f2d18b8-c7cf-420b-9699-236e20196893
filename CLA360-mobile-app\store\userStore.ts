import { useMutation } from "@tanstack/react-query";
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";
import { type SignInReq, SignUpReq, userService } from "@/api/index";
import { APIError } from "@/api/apiClient";
import Toast from "react-native-toast-message";
import * as SecureStore from "expo-secure-store";

import type { UserInfo, UserToken } from "../types/entity";
import { StorageEnum } from "../types/enum";

// Custom storage implementation using SecureStore
const secureStorage = {
    getItem: async (name: string): Promise<string | null> => {
        try {
            return await SecureStore.getItemAsync(name);
        } catch (error) {
            console.error("SecureStore getItem error:", error);
            return null;
        }
    },
    setItem: async (name: string, value: string): Promise<void> => {
        try {
            await SecureStore.setItemAsync(name, value);
        } catch (error) {
            console.error("SecureStore setItem error:", error);
        }
    },
    removeItem: async (name: string): Promise<void> => {
        try {
            await SecureStore.deleteItemAsync(name);
        } catch (error) {
            console.error("SecureStore removeItem error:", error);
        }
    },
};

type UserStore = {
    userInfo: Partial<UserInfo>;
    userToken: UserToken;
    actions: {
        setUserInfo: (userInfo: UserInfo) => void;
        setUserToken: (token: UserToken) => void;
        clearUserInfoAndToken: () => void;
    };
};

const useUserStore = create<UserStore>()(
    persist(
        (set) => ({
            userInfo: {},
            userToken: {},
            actions: {
                setUserInfo: (userInfo) => {
                    set({ userInfo });
                },
                setUserToken: (userToken) => {
                    set({ userToken });
                },
                clearUserInfoAndToken() {
                    // logOut();
                    set({ userInfo: {}, userToken: {} });
                },
            },
        }),
        {
            name: "userStore",
            storage: createJSONStorage(() => secureStorage),
            partialize: (state) => ({
                [StorageEnum.UserInfo]: state.userInfo,
                [StorageEnum.UserToken]: state.userToken,
            }),
        }
    )
);

export const useUserInfo = () => useUserStore((state) => state.userInfo);
export const useUserToken = () => useUserStore((state) => state.userToken);

export const useUserActions = () => useUserStore((state) => state.actions);

export const useSignIn = () => {
    // We're using window.location.href instead of navigate
    const { setUserToken, setUserInfo } = useUserActions();
    const signInMutation = useMutation({
        mutationFn: userService.signin,
        onSuccess: async (data) => {
            setUserInfo(data as UserInfo);
            setUserToken({ access_token: data.access_token });
            Toast.show({
                type: "success",
                text1: "Sign in success!",
            });
        },
        onError: (err: any) => {
            // Use user-friendly message from APIError if available
            const errorMessage =
                err instanceof APIError
                    ? err.userMessage
                    : err.message || "Sign in failed";

            Toast.show({
                type: "error",
                text1: errorMessage,
            });
        },
    });

    const signIn = async (data: SignInReq) => {
        await signInMutation.mutateAsync(data);
    };

    return signIn;
};
export const useLogOut = () => {
    const { clearUserInfoAndToken } = useUserActions();
    const logOutMutation = useMutation({
        mutationFn: userService.logout,
        onSuccess: () => {
            Toast.show({
                type: "success",
                text1: "Logout successful!",
            });
            clearUserInfoAndToken();
        },
        onError: (err: any) => {
            // Use user-friendly message from APIError if available
            const errorMessage =
                err instanceof APIError
                    ? err.userMessage
                    : err.message || "Logout failed";

            Toast.show({
                type: "error",
                text1: errorMessage,
            });
            // Clear local data even if server logout fails
            clearUserInfoAndToken();
        },
    });

    const logOut = async () => {
        await logOutMutation.mutateAsync();
    };

    return logOut;
};

export const useSignUp = () => {
    // We're using window.location.href instead of navigate
    const { setUserToken, setUserInfo } = useUserActions();
    const signUpMutation = useMutation({
        mutationFn: userService.signup,
        onSuccess: async (data) => {
            // Then update the store state
            setUserInfo(data);
            setUserToken({ access_token: data.access_token });

            Toast.show({
                type: "success",
                text1: "Sign up success!",
            });
        },
        // Add onError handler
        onError: (err: any) => {
            // Use user-friendly message from APIError if available
            const errorMessage =
                err instanceof APIError
                    ? err.userMessage
                    : err.message || "Sign up failed";

            Toast.show({
                type: "error",
                text1: errorMessage,
            });
        },
    });

    const signUp = async (data: SignUpReq) => {
        await signUpMutation.mutateAsync(data);
    };

    return signUp;
};

export default useUserStore;
