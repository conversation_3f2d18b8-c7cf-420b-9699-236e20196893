import { BlurView } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';
import { View, Text, Image, StyleSheet, TouchableOpacity } from 'react-native';

export default function Card({ event, onPress }: { event: any; onPress?: () => void }) {
    const CardContent = () => (
        <View className="h-full w-full justify-end overflow-hidden rounded-3xl">
            <Image source={event.image} className="absolute h-full w-full" resizeMode="cover" />
            <BlurView intensity={20} className="h-24 w-full justify-center">
                <LinearGradient
                    // Background Linear Gradient
                    colors={['transparent', 'rgba(0, 0, 0, 0.6)', 'rgba(0,0,0,0.8)']}
                    style={StyleSheet.absoluteFill}
                />
                <Text className="text-center text-xl text-white font-JakartaBold px-4" numberOfLines={2} ellipsizeMode="tail">
                    {event.title}
                </Text>
            </BlurView>
        </View>
    );

    if (onPress) {
        return (
            <TouchableOpacity onPress={onPress} className="h-full w-full">
                <CardContent />
            </TouchableOpacity>
        );
    }

    return <CardContent />;
}
