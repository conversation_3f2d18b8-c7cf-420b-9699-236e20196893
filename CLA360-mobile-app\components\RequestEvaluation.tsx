import { Text, TouchableOpacity, View, Image } from 'react-native'
import React from 'react'
import { images } from '@/constants'
import { router } from 'expo-router'
import { ChartBarIcon, ArrowRightIcon } from 'react-native-heroicons/outline'

const RequestEvaluation = () => {
    return (
        <TouchableOpacity
            onPress={() => {
                router.push("/(root)/evaluations")
            }}
            className="mt-6"
        >
            <View className="bg-white rounded-3xl p-6 shadow-lg shadow-gray-200/50 border border-gray-100">
                <View className="flex flex-row items-center justify-between">
                    <View className="flex-1">
                        <View className="flex flex-row items-center mb-3">
                            {/* <View className="w-12 h-12 bg-blue-100 rounded-2xl items-center justify-center mr-4">
                                <ChartBarIcon color="#3b82f6" size={24} />
                            </View> */}
                            <View className="flex-1">
                                <Text className="text-xl font-JakartaBold text-gray-900 mb-1">
                                    Evaluations
                                </Text>
                                <Text className="text-sm font-JakartaMedium text-gray-600">
                                    Get personalized insights from CLA360
                                </Text>
                            </View>
                        </View>

                        {/* <View className="flex flex-row items-center">
                            <Text className="text-sm font-JakartaBold text-blue-600 mr-2">
                                View Analysis
                            </Text>
                            <ArrowRightIcon color="#3b82f6" size={16} />
                        </View> */}
                    </View>

                    <View className="ml-4">
                        <Image
                            source={images.transcript}
                            className="w-20 h-20"
                            resizeMode="contain"
                        />
                    </View>
                </View>
            </View>
        </TouchableOpacity>
    )
}

export default RequestEvaluation
