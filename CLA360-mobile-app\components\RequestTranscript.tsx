import { Text, TouchableOpacity, View, Image } from 'react-native'
import React from 'react'
import { images } from '@/constants'
import { router } from 'expo-router'
import { DocumentTextIcon, ArrowRightIcon } from 'react-native-heroicons/outline'

const RequestTranscript = () => {
    return (
        <TouchableOpacity
            onPress={() => {
                router.push("/(root)/transcripts")
            }}
            className="mt-6"
        >
            <View className="bg-white rounded-3xl p-6 shadow-lg shadow-gray-200/50 border border-gray-100">
                <View className="flex flex-row items-center justify-between">
                    <View className="flex-1">
                        <View className="flex flex-row items-center mb-3">
                            {/* <View className="w-12 h-12 bg-red-100 rounded-2xl items-center justify-center mr-4">
                                <DocumentTextIcon color="#ef4444" size={24} />
                            </View> */}
                            <View className="flex-1">
                                <Text className="text-lg font-JakartaBold text-gray-900 mb-1">
                                    Transcripts
                                </Text>
                                <Text className="text-sm font-JakartaMedium text-gray-600">
                                    Request and view your academic transcripts
                                </Text>
                            </View>
                        </View>

                        {/* <View className="flex flex-row items-center">
                            <Text className="text-sm font-JakartaBold text-red-600 mr-2">
                                Manage Documents
                            </Text>
                            <ArrowRightIcon color="#ef4444" size={16} />
                        </View> */}
                    </View>

                    <View className="ml-4">
                        <Image
                            source={images.transcript1}
                            className="w-20 h-20"
                            resizeMode="contain"
                        />
                    </View>
                </View>
            </View>
        </TouchableOpacity>
    )
}

export default RequestTranscript
