import axios, {
    type AxiosRequestConfig,
    type AxiosError,
    type AxiosResponse,
} from "axios";

import userStore from "@/store/userStore";

import type { Result } from "@/types/api";
import { ResultEnum } from "@/types/enum";

// Custom error class for API errors
export class APIError extends Error {
    public status?: number;
    public userMessage: string;
    public originalError?: any;

    constructor(
        message: string,
        userMessage: string,
        status?: number,
        originalError?: any
    ) {
        super(message);
        this.name = "APIError";
        this.userMessage = userMessage;
        this.status = status;
        this.originalError = originalError;
    }
}

// Utility function to extract user-friendly error messages
const getUserFriendlyErrorMessage = (
    error: AxiosError<any>,
    defaultMessage: string = "Something went wrong"
): string => {
    // Try to get message from response data
    if (error.response?.data?.message) {
        return error.response.data.message;
    }
    
    // Try to get message from response data errors (for validation errors)
    if (error.response?.data?.errors) {
        const errors = error.response.data.errors;
        if (typeof errors === "object") {
            // Get first error message from validation errors
            const firstErrorKey = Object.keys(errors)[0];
            if (firstErrorKey && errors[firstErrorKey]?.[0]) {
                return errors[firstErrorKey][0];
            }
        }
    }

    // Handle specific HTTP status codes
    switch (error.response?.status) {
        case 400:
            return "Invalid request. Please check your input and try again.";
        case 401:
            return "Your session has expired. Please sign in again.";
        case 403:
            return "You don't have permission to perform this action.";
        case 404:
            return "The requested resource was not found.";
        case 422:
            return "Please check your input and try again.";
        case 429:
            return "Too many requests. Please wait a moment and try again.";
        case 500:
            return "Server error. Please try again later.";
        case 503:
            return "Service temporarily unavailable. Please try again later.";
        default:
            return defaultMessage;
    }
};

const axiosInstance = axios.create({
    baseURL: "https://staging.cla360.app/api/v1",
    timeout: 50000,
    headers: { "Content-Type": "application/json;charset=utf-8" },
});

// Request interceptor
axiosInstance.interceptors.request.use(
    (config) => {
        const token = userStore.getState().userToken.access_token;
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Response interceptor
axiosInstance.interceptors.response.use(
    (res: AxiosResponse<Result>) => {
        if (!res.data) {
            throw new APIError(
                "Api request failed",
                "No response data received",
                res.status
            );
        }

        if (res.data.data) {
            return res.data.data;
        } else if (res.data) {
            return res.data;
        }

        const { status, data, message } = res.data;
        const hasSuccess =
            data &&
            Reflect.has(res.data, "status") &&
            status === ResultEnum.SUCCESS;
        if (hasSuccess) {
            return data;
        }

        throw new APIError(
            message || "Api request failed",
            message || "Request failed. Please try again.",
            status
        );
    },
    (error: AxiosError<Result>) => {
        const { response } = error || {};
        const status = response?.status;

        // Handle 401 unauthorized - clear user session
        if (status === 401) {
            userStore.getState().actions.clearUserInfoAndToken();
        }

        // Create user-friendly error message
        const userMessage = getUserFriendlyErrorMessage(error);
        const technicalMessage = error.message || "Api request failed";

        // Create custom API error with user-friendly message
        const apiError = new APIError(
            technicalMessage,
            userMessage,
            status,
            error
        );

        return Promise.reject(apiError);
    }
);

class APIClient {
    get<T = any>(config: AxiosRequestConfig): Promise<T> {
        return this.request({ ...config, method: "GET" });
    }

    post<T = any>(config: AxiosRequestConfig): Promise<T> {
        return this.request({ ...config, method: "POST" });
    }

    put<T = any>(config: AxiosRequestConfig): Promise<T> {
        return this.request({ ...config, method: "PUT" });
    }

    delete<T = any>(config: AxiosRequestConfig): Promise<T> {
        return this.request({ ...config, method: "DELETE" });
    }

    request<T = any>(config: AxiosRequestConfig): Promise<T> {
        return new Promise((resolve, reject) => {
            axiosInstance
                .request<any, AxiosResponse<Result>>(config)
                .then((res: AxiosResponse<Result>) => {
                    resolve(res as unknown as Promise<T>);
                })
                .catch((e: Error | AxiosError) => {
                    reject(e);
                });
        });
    }
}

export default new APIClient();
