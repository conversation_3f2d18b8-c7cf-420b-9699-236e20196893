import React, { useState } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import {
    CheckCircleIcon,
    DocumentTextIcon,
    ChevronDownIcon,
    ChevronRightIcon,
    UserIcon,
    AcademicCapIcon,
    ClipboardDocumentListIcon,
    HeartIcon,
    BriefcaseIcon,
    InformationCircleIcon
} from 'react-native-heroicons/outline';
import { CheckCircleIcon as CheckCircleIconSolid } from 'react-native-heroicons/solid';
import Animated, {
    FadeInDown,
    FadeInUp,
    FadeInLeft
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';

interface StepData {
    id: string;
    title: string;
    description: string;
    status: 'completed' | 'current' | 'pending';
    icon: any;
    items?: string[];
}

const ApplicationJourneyTracker = () => {
    const [expandedSteps, setExpandedSteps] = useState<string[]>([]);

    const steps: StepData[] = [
        {
            id: 'profile',
            title: 'Complete Your Profile',
            description: 'Add your academic history, achievements, and preferences',
            status: 'completed',
            icon: UserIcon,
            items: [
                'Personal Information',
                'Guardian Information',
                'Academic Background',
                'Program Preferences'
            ]
        },
        {
            id: 'documents',
            title: 'Upload Documents',
            description: 'Submit your transcripts, recommendations, and test scores',
            status: 'completed',
            icon: DocumentTextIcon,
            items: [
                'Official Transcripts',
                'Letters of Recommendation',
                'Test Scores (SAT/ACT)',
                'Personal Statement'
            ]
        },
        {
            id: 'activities',
            title: 'Extracurricular Activities',
            description: 'List your activities, achievements, and leadership roles',
            status: 'current',
            icon: AcademicCapIcon,
            items: [
                'Academic Honors',
                'Sports & Athletics',
                'Volunteer Work',
                'Leadership Positions'
            ]
        },
        {
            id: 'experience',
            title: 'Work Experience',
            description: 'Add any relevant work or internship experience',
            status: 'pending',
            icon: BriefcaseIcon,
            items: [
                'Part-time Jobs',
                'Internships',
                'Summer Programs',
                'Research Experience'
            ]
        },
        {
            id: 'health',
            title: 'Health Information',
            description: 'Provide necessary health and medical information',
            status: 'pending',
            icon: HeartIcon,
            items: [
                'Medical History',
                'Immunization Records',
                'Emergency Contacts',
                'Insurance Information'
            ]
        },
        {
            id: 'additional',
            title: 'Additional Information',
            description: 'Any other relevant information for your application',
            status: 'pending',
            icon: InformationCircleIcon,
            items: [
                'Special Circumstances',
                'Financial Aid Forms',
                'Scholarship Applications',
                'Housing Preferences'
            ]
        }
    ];

    const toggleStep = (stepId: string) => {
        setExpandedSteps(prev =>
            prev.includes(stepId)
                ? prev.filter(id => id !== stepId)
                : [...prev, stepId]
        );
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'completed': return 'bg-green-500';
            case 'current': return 'bg-blue-500';
            case 'pending': return 'bg-gray-300';
            default: return 'bg-gray-300';
        }
    };

    const getStatusIcon = (status: string, IconComponent: any) => {
        if (status === 'completed') {
            return <CheckCircleIconSolid color="white" size={20} />;
        }
        return <IconComponent color="white" size={20} />;
    };

    return (
        <View className="mb-8">
            {/* Header */}
            <Animated.View
                entering={FadeInDown.delay(100).duration(600)}
                className="mb-6"
            >
                <Text className="font-JakartaBold text-2xl text-gray-900 mb-2">
                    Your Application Journey
                </Text>
                <Text className="font-JakartaMedium text-base text-gray-600">
                    Track your progress through the application process
                </Text>
            </Animated.View>

            {/* Progress Overview */}
            <Animated.View
                entering={FadeInUp.delay(200).duration(600)}
                className="bg-white rounded-2xl p-5 mb-6 shadow-lg shadow-gray-200/50"
            >
                <View className="flex flex-row items-center justify-between mb-4">
                    <Text className="font-JakartaBold text-lg text-gray-900">
                        Overall Progress
                    </Text>
                    <View className="bg-primary-100 px-3 py-1 rounded-full">
                        <Text className="font-JakartaBold text-sm text-primary-600">
                            2 of 6 completed
                        </Text>
                    </View>
                </View>

                {/* Progress Bar */}
                <View className="w-full h-3 bg-gray-100 rounded-full overflow-hidden">
                    <Animated.View
                        entering={FadeInLeft.delay(400).duration(1000)}
                        className="h-full w-[33%] rounded-full"
                    >
                        <LinearGradient
                            colors={['#0286FF', '#6A85E6']}
                            start={{ x: 0, y: 0 }}
                            end={{ x: 1, y: 0 }}
                            className="h-full w-full rounded-full"
                        />
                    </Animated.View>
                </View>
            </Animated.View>

            {/* Progress Steps */}
            <View className="space-y-4">
                {steps.map((step, index) => {
                    const isExpanded = expandedSteps.includes(step.id);
                    const isLast = index === steps.length - 1;

                    return (
                        <Animated.View
                            key={step.id}
                            entering={FadeInUp.delay(300 + index * 100).duration(600)}
                            className="relative"
                        >
                            {/* Connecting Line */}
                            {!isLast && (
                                <View className="absolute left-6 top-16 w-0.5 h-8 bg-gray-200 z-0" />
                            )}

                            {/* Step Card */}
                            <TouchableOpacity
                                onPress={() => toggleStep(step.id)}
                                className="bg-white rounded-2xl p-5 shadow-md shadow-gray-200/30 border border-gray-100 z-10"
                            >
                                <View className="flex flex-row items-center">
                                    {/* Status Icon */}
                                    <View className={`w-12 h-12 rounded-full items-center justify-center mr-4 ${getStatusColor(step.status)} shadow-lg`}>
                                        {getStatusIcon(step.status, step.icon)}
                                    </View>

                                    {/* Step Content */}
                                    <View className="flex-1">
                                        <View className="flex flex-row items-center justify-between mb-1">
                                            <Text className="font-JakartaBold text-lg text-gray-900">
                                                {step.title}
                                            </Text>
                                            {step.status === 'completed' && (
                                                <View className="bg-success-100 px-3 py-1 rounded-full">
                                                    <Text className="font-JakartaBold text-xs text-success-700">
                                                        ✓
                                                    </Text>
                                                </View>
                                            )}
                                            {step.status === 'current' && (
                                                <View className="bg-warning-100 px-3 py-1 rounded-full">
                                                    <Text className="font-JakartaBold text-xs text-warning-700">
                                                        ⏳
                                                    </Text>
                                                </View>
                                            )}
                                        </View>
                                        <Text className="font-JakartaMedium text-sm text-gray-600 mb-3">
                                            {step.description}
                                        </Text>

                                        {/* Expand/Collapse Button */}
                                        <View className="flex flex-row items-center">
                                            <Text className="font-JakartaMedium text-sm text-primary-600 mr-2">
                                                {isExpanded ? 'Hide details' : 'View details'}
                                            </Text>
                                            {isExpanded ? (
                                                <ChevronDownIcon color="#0286FF" size={18} />
                                            ) : (
                                                <ChevronRightIcon color="#0286FF" size={18} />
                                            )}
                                        </View>
                                    </View>
                                </View>
                            </TouchableOpacity>

                            {/* Expandable Content */}
                            {isExpanded && (
                                <Animated.View
                                    entering={FadeInDown.duration(300)}
                                    className="ml-16 mt-3 p-4 bg-gray-50 rounded-xl border border-gray-100"
                                >
                                    <Text className="font-JakartaBold text-sm text-gray-700 mb-3">
                                        Required Items:
                                    </Text>
                                    {step.items?.map((item, itemIndex) => (
                                        <View key={itemIndex} className="flex flex-row items-center py-2">
                                            <View className={`w-2 h-2 rounded-full mr-3 ${step.status === 'completed' ? 'bg-success-500' : 'bg-gray-300'
                                                }`} />
                                            <Text className="font-JakartaMedium text-sm text-gray-700 flex-1">
                                                {item}
                                            </Text>
                                            {step.status === 'completed' && (
                                                <CheckCircleIconSolid color="#22c55e" size={16} />
                                            )}
                                        </View>
                                    ))}
                                </Animated.View>
                            )}
                        </Animated.View>
                    );
                })}
            </View>
        </View>
    );
};

export default ApplicationJourneyTracker;
