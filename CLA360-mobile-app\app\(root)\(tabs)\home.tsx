import { images } from "@/constants";
import { useAuth } from "@/hooks/useAuth";
import { router } from "expo-router";
import { ScrollView, Text, TouchableOpacity, View } from "react-native";
import { BellIcon, UserIcon } from "react-native-heroicons/outline"
import { SafeAreaView } from "react-native-safe-area-context";
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
    FadeInDown,
    FadeInUp,
    FadeInLeft,
    FadeInRight
} from 'react-native-reanimated';
import Exhibition from "@/components/pages/main/homeScroll";
import ApplicationJourneyTracker from "@/components/pages/main/ApplicationJourneyTracker ";

export default function Page() {
    const { user } = useAuth();

    return (
        <SafeAreaView className="bg-neutral-50 h-full">
            {/* Header Section */}
            <Animated.View
                entering={FadeInDown.duration(600)}
                className="px-6 py-5"
            >
                <View className="w-full flex flex-row justify-between items-center mb-6">
                    <Animated.Image
                        entering={FadeInLeft.delay(200).duration(600)}
                        source={images.logo}
                        className="w-[70px] h-[50px]"
                        resizeMode="contain"
                    />
                    <Animated.View
                        entering={FadeInRight.delay(300).duration(600)}
                        className="flex flex-row gap-3"
                    >
                        <TouchableOpacity
                            className="rounded-full bg-primary-500 p-3 shadow-lg shadow-primary-500/25"
                            onPress={() => {
                                router.push("/(root)/notification");
                            }}
                        >
                            <BellIcon color="white" size={20} />
                        </TouchableOpacity>
                        <TouchableOpacity
                            className="rounded-full bg-primary-500 p-3 shadow-lg shadow-primary-500/25"
                            onPress={() => {
                                router.push("/profile");
                            }}
                        >
                            <UserIcon color="white" size={20} />
                        </TouchableOpacity>
                    </Animated.View>
                </View>

                {/* Welcome Section - Enhanced Design */}
                <Animated.View entering={FadeInUp.delay(400).duration(800)}>
                    <LinearGradient
                        colors={['#6366f1', '#8b5cf6', '#a855f7']}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                        className="rounded-3xl p-6 shadow-xl shadow-purple-500/20"
                        style={{ borderRadius: 20 }}
                    >
                        <View className="flex flex-row items-center justify-between">
                            <View className="flex-1">
                                <Text className="font-JakartaBold text-2xl text-white mb-1">
                                    Welcome back!
                                </Text>
                                <Text className="font-JakartaBold text-lg text-white/90 mb-2">
                                    {user?.full_name || 'Student'}
                                </Text>
                                <Text className="font-JakartaMedium text-sm text-white/70">
                                    ID: {user?.cla_user_id || 'CLA-8220438'}
                                </Text>
                            </View>
                            <View className="items-center">
                                <View className="w-16 h-16 rounded-full bg-white/20 items-center justify-center mb-2">
                                    <Text className="font-JakartaBold text-xl text-white">65%</Text>
                                </View>
                                <Text className="font-JakartaMedium text-xs text-white/80 text-center">
                                    Profile{'\n'}Complete
                                </Text>
                            </View>
                        </View>

                        {/* Progress Bar */}
                        <View className="mt-4">
                            <View className="w-full h-2 bg-white/20 rounded-full overflow-hidden">
                                <Animated.View
                                    entering={FadeInLeft.delay(800).duration(1000)}
                                    className="h-full w-[65%] bg-white rounded-full"
                                />
                            </View>
                        </View>
                    </LinearGradient>
                </Animated.View>
            </Animated.View>

            {/* Main Content */}
            <ScrollView
                className="flex-1 px-6 "
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{ paddingBottom: 32 }}

            >
                <View style={{ height: 200 }} className="mb-6">
                    <Exhibition />
                </View>

                <Animated.View entering={FadeInLeft.delay(600).duration(800)}>
                    <ApplicationJourneyTracker />
                </Animated.View>
            </ScrollView>
            {/* <ScrollView
                className="flex-1 px-6"
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{ paddingBottom: 32 }}
            >
                <Animated.View entering={FadeInLeft.delay(600).duration(800)}>
                    <ApplicationJourneyTracker />
                </Animated.View>

                <Animated.View entering={FadeInRight.delay(700).duration(800)}>
                    <RequestTranscript />
                </Animated.View>

                <Animated.View entering={FadeInDown.delay(800).duration(800)}>
                    <RequestEvaluation />
                </Animated.View>

                <Animated.View entering={FadeInUp.delay(900).duration(800)}>
                    <StartApplications />
                </Animated.View>
            </ScrollView> */}
        </SafeAreaView>
    );
}
